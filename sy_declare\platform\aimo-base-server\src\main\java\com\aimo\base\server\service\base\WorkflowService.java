
package com.aimo.base.server.service.base;

import com.aimo.base.client.model.base.Workflow;
import com.aimo.common.mybatis.base.service.IBaseService;
import com.aimo.common.model.PageParams;
import com.aimo.common.model.ResultBody;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 工作流服务接口
 *
 * <AUTHOR>
 */
public interface WorkflowService extends IBaseService<Workflow> {
    
    /**
     * 分页查询工作流
     *
     * @param pageParams 分页参数
     * @return 分页结果
     */
    IPage<Workflow> listPage(PageParams<Workflow> pageParams);
    
    /**
     * 根据工作流Key查询工作流
     *
     * @param workflowKey 工作流Key
     * @return 工作流
     */
    Workflow getByWorkflowKey(String workflowKey);
    
    /**
     * 检查工作流Key是否存在
     *
     * @param workflowKey 工作流Key
     * @return 是否存在
     */
    boolean checkWorkflowKey(String workflowKey);
    
    /**
     * 保存工作流
     *
     * @param workflow 工作流
     * @return 是否成功
     */
    boolean saveWorkflow(Workflow workflow);
    
    /**
     * 更新工作流
     *
     * @param workflow 工作流
     * @return 是否成功
     */
    boolean updateWorkflow(Workflow workflow);
    
    /**
     * 删除工作流
     *
     * @param id 工作流ID
     * @return 是否成功
     */
    boolean deleteWorkflow(Long id);
    
    /**
     * 获取所有启用的工作流
     *
     * @return 工作流列表
     */
    List<Workflow> getEnabledWorkflows();
    
    /**
     * 部署工作流
     *
     * @param id 工作流ID
     * @return 是否成功
     */
    boolean deployWorkflow(Long id);
    
    /**
     * 取消部署工作流
     *
     * @param id 工作流ID
     * @return 是否成功
     */
    boolean undeployWorkflow(Long id);

    // 现有实现类中的方法签名，保持兼容性

    /**
     * 分页查询工作流
     *
     * @param page 页码
     * @param size 页大小
     * @param workflowName 工作流名称
     * @param execute 执行状态
     * @return 分页结果
     */
    ResultBody<IPage<Workflow>> getWorkflowPage(Integer page, Integer size, String workflowName, Integer execute);

    /**
     * 根据ID查询工作流
     *
     * @param id 工作流ID
     * @return 工作流
     */
    ResultBody<Workflow> getWorkflowById(Long id);

    /**
     * 新增工作流
     *
     * @param workflow 工作流
     * @return 结果
     */
    ResultBody<String> addWorkflow(Workflow workflow);

    /**
     * 更新工作流（带返回结果）
     *
     * @param workflow 工作流
     * @return 结果
     */
    ResultBody<String> updateWorkflowWithResult(Workflow workflow);

    /**
     * 删除工作流（带返回结果）
     *
     * @param id 工作流ID
     * @return 结果
     */
    ResultBody<String> deleteWorkflowWithResult(Long id);

    /**
     * 检查工作流Key是否存在
     *
     * @param workflowKey 工作流Key
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean existsByWorkflowKey(String workflowKey, Long excludeId);

    /**
     * 保存BPMN流程
     *
     * @param workflow 工作流
     * @return 结果
     */
    ResultBody<String> saveBpmnProcess(Workflow workflow);

    /**
     * 上传BPMN文件
     *
     * @param workflowKey 工作流Key
     * @param bpmnXml BPMN XML内容
     * @return 结果
     */
    ResultBody<String> uploadBpmnFile(String workflowKey, String bpmnXml);
}
