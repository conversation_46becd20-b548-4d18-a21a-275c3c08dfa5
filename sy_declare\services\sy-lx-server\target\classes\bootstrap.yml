server:
    port: 8234
    servlet:
        session:
            cookie:
                name: OAUTH2SESSION
spring:
    application:
        name: sy-lx-server
    cloud:
        #手动配置Bus id,
        bus:
            id: ${spring.application.name}:${server.port}
        nacos:
            config:
                enabled: true
                username: nacos
                password: sy@nacos2022
                file-extension: properties
                shared-configs[0]:
                    data-id: common.properties
                    refresh: true
                    group: DEFAULT_GROUP
                shared-configs[1]:
                    data-id: db.properties
                    refresh: true
                    group: DEFAULT_GROUP
                shared-configs[2]:
                    data-id: redis.properties
                    refresh: true
                    group: DEFAULT_GROUP
                shared-configs[3]:
                    data-id: rabbitmq.properties
                    refresh: true
                    group: DEFAULT_GROUP

    main:
        allow-bean-definition-overriding: true
    #解决restful 404错误 spring.mvc.throw-exception-if-no-handler-found=true spring.resources.add-mappings=false
    mvc:
        throw-exception-if-no-handler-found: true
    resources:
        add-mappings: false
    profiles:
        active: prod
      # 文件上传限制
    servlet:
        multipart:
            max-file-size: 10MB
            max-request-size: 10MB
    thymeleaf:
        cache: false
        encoding: UTF-8
        mode: LEGACYHTML5
        prefix: classpath:/templates/
        suffix: .html

management:
    endpoints:
        web:
            exposure:
                include: '*'
feign:
    compression:
      request:
          enabled: false
      response:
          enabled: false
aimo:
    swagger2:
        enabled: true
        description: 平台用户认证服务器
        title: 平台用户认证服务器
    client:
        oauth2:
            admin:
                client-id: 7gBZcbsC7kLIWCdELIl8nxcs
                client-secret: 0osTIhce7uPvDKHz6aa67bhCukaKoYl4
lingXing:
    app:
        key: ak_ZPQrCExhB052y
        secret: f4r5OdSI/cMtvdsQzAGL+g==
#mybatis plus 设置
mybatis-plus:
 #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.sy.lingxing.client.**.entity
  mapper-locations: classpath:mapper/*.xml
