<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="df94a903-579d-4ffd-9f16-f737eab78f46" name="更改" comment="上新申请功能增加审批流流程&#10;把写死的审批流模块数据源路径改成以nacos配置的数据源&#10;完善工作审批流的业务代码">
      <change afterPath="$PROJECT_DIR$/platform/aimo-base-server/src/main/resources/sql/base_fun_element.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-api-spring-server/src/main/resources/bootstrap-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-api-spring-server/src/main/resources/bootstrap-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-api-spring-server/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-api-spring-server/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/controller/base/LoginController.java" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/controller/base/LoginController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/service/custom/impl/LogisticsProviderEmailServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/service/custom/impl/LogisticsProviderEmailServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/utils/IPMacUtils.java" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/utils/IPMacUtils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-base-server/src/main/resources/bootstrap-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-base-server/src/main/resources/bootstrap-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-base-server/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-base-server/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/platform/aimo-base-server/src/main/resources/mapper/custom/DeclarationBookingMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/platform/aimo-base-server/src/main/resources/mapper/custom/DeclarationBookingMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-erp-server/src/main/resources/bootstrap-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-erp-server/src/main/resources/bootstrap-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-lx-server/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-lx-server/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-lx-server/src/main/java/com/sy/lingxing/server/controller/AwdInventoryController.java" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-lx-server/src/main/java/com/sy/lingxing/server/controller/AwdInventoryController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-lx-server/src/main/java/com/sy/lingxing/server/controller/ListingController.java" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-lx-server/src/main/java/com/sy/lingxing/server/controller/ListingController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-lx-server/src/main/java/com/sy/lingxing/server/service/impl/LxAwdInventoryServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-lx-server/src/main/java/com/sy/lingxing/server/service/impl/LxAwdInventoryServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-lx-server/src/main/java/com/sy/lingxing/server/service/impl/LxFbaInventoryServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-lx-server/src/main/java/com/sy/lingxing/server/service/impl/LxFbaInventoryServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-lx-server/src/main/resources/bootstrap-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-lx-server/src/main/resources/bootstrap-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-ys-client/src/main/java/com/sy/ys/client/model/base/YsProduct.java" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-ys-client/src/main/java/com/sy/ys/client/model/base/YsProduct.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-ys-server/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-ys-server/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/sy-ys-server/src/main/resources/bootstrap-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/services/sy-ys-server/src/main/resources/bootstrap-local.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
        <option value="Interface" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="30aZW4zv41E0D6Nn6j48fJh1d3v" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.aimo-base-client [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.aimo-base-client [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.aimo-common-core [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.aimo-common-core [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.sy-ys-client [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.sy-ys-client [install].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.BaseApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.ErpApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.GatewaySpringApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.LingXingApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.YoungSuiteApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;模块&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.BaseApplication">
    <configuration name="BaseApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="aimo-base-server" />
      <option name="SHORTEN_COMMAND_LINE" value="CLASSPATH_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.aimo.base.server.BaseApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ErpApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="sy-erp-server" />
      <option name="SHORTEN_COMMAND_LINE" value="CLASSPATH_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.sy.erp.server.ErpApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewaySpringApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="aimo-api-spring-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.aimo.gateway.spring.server.GatewaySpringApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LingXingApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="sy-lx-server" />
      <option name="SHORTEN_COMMAND_LINE" value="CLASSPATH_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.sy.lingxing.server.LingXingApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="YoungSuiteApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="sy-ys-server" />
      <option name="SHORTEN_COMMAND_LINE" value="CLASSPATH_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.sy.ys.server.YoungSuiteApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Spring Boot.BaseApplication" />
      <item itemvalue="Spring Boot.ErpApplication" />
      <item itemvalue="Spring Boot.GatewaySpringApplication" />
      <item itemvalue="Spring Boot.LingXingApplication" />
      <item itemvalue="Spring Boot.YoungSuiteApplication" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="df94a903-579d-4ffd-9f16-f737eab78f46" name="更改" comment="" />
      <created>1753867673719</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753867673719</updated>
      <workItem from="1753867675277" duration="688000" />
      <workItem from="1753868377718" duration="268000" />
      <workItem from="1753868654459" duration="21953000" />
      <workItem from="1753944771269" duration="28639000" />
      <workItem from="1754033255208" duration="8386000" />
      <workItem from="1754267452486" duration="32814000" />
      <workItem from="1754355091423" duration="7153000" />
      <workItem from="1754362684649" duration="1396000" />
    </task>
    <task id="LOCAL-00001" summary="提交部分工作审批流服务端代码">
      <option name="closed" value="true" />
      <created>1753870011713</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753870011713</updated>
    </task>
    <task id="LOCAL-00002" summary="提交erp模块的pom依赖文件">
      <option name="closed" value="true" />
      <created>1753923178809</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753923178809</updated>
    </task>
    <task id="LOCAL-00003" summary="提交base的pom依赖">
      <option name="closed" value="true" />
      <created>1753923656787</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753923656787</updated>
    </task>
    <task id="LOCAL-00004" summary="去除多余文件，排查启动失败的原因">
      <option name="closed" value="true" />
      <created>1753928105708</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753928105708</updated>
    </task>
    <task id="LOCAL-00005" summary="提交初版工作流模块代码">
      <option name="closed" value="true" />
      <created>1753947685510</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753947685510</updated>
    </task>
    <task id="LOCAL-00006" summary="去除ErpWorkflowFeignConfig.java">
      <option name="closed" value="true" />
      <created>1754035425318</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1754035425318</updated>
    </task>
    <task id="LOCAL-00007" summary="上新申请功能增加审批流流程&#10;把写死的审批流模块数据源路径改成以nacos配置的数据源&#10;完善工作审批流的业务代码">
      <option name="closed" value="true" />
      <created>1754363732922</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1754363732922</updated>
    </task>
    <option name="localTasksCounter" value="8" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="提交部分工作审批流服务端代码" />
    <MESSAGE value="提交erp模块的pom依赖文件" />
    <MESSAGE value="提交base的pom依赖" />
    <MESSAGE value="去除多余文件，排查启动失败的原因" />
    <MESSAGE value="提交初版工作流模块代码" />
    <MESSAGE value="去除ErpWorkflowFeignConfig.java" />
    <MESSAGE value="上新申请功能增加审批流流程&#10;把写死的审批流模块数据源路径改成以nacos配置的数据源&#10;完善工作审批流的业务代码" />
    <option name="LAST_COMMIT_MESSAGE" value="上新申请功能增加审批流流程&#10;把写死的审批流模块数据源路径改成以nacos配置的数据源&#10;完善工作审批流的业务代码" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/service/base/impl/WechatMessageServiceImpl.java</url>
          <line>71</line>
          <option name="timeStamp" value="83" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/services/sy-erp-server/src/main/java/com/sy/erp/server/controller/ErpWorkflowController.java</url>
          <line>86</line>
          <option name="timeStamp" value="95" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/services/sy-erp-server/src/main/java/com/sy/erp/server/service/impl/WorkflowServiceImpl.java</url>
          <line>83</line>
          <option name="timeStamp" value="96" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/services/sy-erp-server/src/main/java/com/sy/erp/server/service/impl/WorkflowServiceImpl.java</url>
          <line>84</line>
          <option name="timeStamp" value="98" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/services/sy-erp-server/src/main/java/com/sy/erp/server/controller/ErpWorkflowController.java</url>
          <line>135</line>
          <option name="timeStamp" value="99" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/services/sy-erp-server/src/main/java/com/sy/erp/server/controller/ErpWorkflowController.java</url>
          <line>79</line>
          <option name="timeStamp" value="101" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/platform/aimo-base-server/src/main/java/com/aimo/base/server/service/newApply/impl/NewApplyServiceImpl.java</url>
          <line>393</line>
          <option name="timeStamp" value="109" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/services/sy-erp-server/src/main/java/com/sy/erp/server/activiti/NotificationDelegate.java</url>
          <line>45</line>
          <option name="timeStamp" value="110" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>